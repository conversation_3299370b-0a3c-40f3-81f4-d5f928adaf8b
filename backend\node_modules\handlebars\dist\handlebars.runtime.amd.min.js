/**!

 @license
 handlebars v4.7.8

Copyright (C) 2011-2019 by <PERSON><PERSON><PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

*/
define("handlebars/utils",["exports"],function(a){"use strict";function b(a){return j[a]}function c(a){for(var b=1;b<arguments.length;b++)for(var c in arguments[b])Object.prototype.hasOwnProperty.call(arguments[b],c)&&(a[c]=arguments[b][c]);return a}function d(a,b){for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1}function e(a){if("string"!=typeof a){if(a&&a.toHTML)return a.toHTML();if(null==a)return"";if(!a)return a+"";a=""+a}return l.test(a)?a.replace(k,b):a}function f(a){return!a&&0!==a||!(!o(a)||0!==a.length)}function g(a){var b=c({},a);return b._parent=a,b}function h(a,b){return a.path=b,a}function i(a,b){return(a?a+".":"")+b}a.__esModule=!0,a.extend=c,a.indexOf=d,a.escapeExpression=e,a.isEmpty=f,a.createFrame=g,a.blockParams=h,a.appendContextPath=i;var j={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},k=/[&<>"'`=]/g,l=/[&<>"'`=]/,m=Object.prototype.toString;a.toString=m;var n=function(a){return"function"==typeof a};n(/x/)&&(a.isFunction=n=function(a){return"function"==typeof a&&"[object Function]"===m.call(a)}),a.isFunction=n;var o=Array.isArray||function(a){return!(!a||"object"!=typeof a)&&"[object Array]"===m.call(a)};a.isArray=o}),define("handlebars/exception",["exports","module"],function(a,b){"use strict";function c(a,b){var e=b&&b.loc,f=void 0,g=void 0,h=void 0,i=void 0;e&&(f=e.start.line,g=e.end.line,h=e.start.column,i=e.end.column,a+=" - "+f+":"+h);for(var j=Error.prototype.constructor.call(this,a),k=0;k<d.length;k++)this[d[k]]=j[d[k]];Error.captureStackTrace&&Error.captureStackTrace(this,c);try{e&&(this.lineNumber=f,this.endLineNumber=g,Object.defineProperty?(Object.defineProperty(this,"column",{value:h,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:i,enumerable:!0})):(this.column=h,this.endColumn=i))}catch(l){}}var d=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];c.prototype=new Error,b.exports=c}),define("handlebars/helpers/block-helper-missing",["exports","module","../utils"],function(a,b,c){"use strict";b.exports=function(a){a.registerHelper("blockHelperMissing",function(b,d){var e=d.inverse,f=d.fn;if(b===!0)return f(this);if(b===!1||null==b)return e(this);if(c.isArray(b))return b.length>0?(d.ids&&(d.ids=[d.name]),a.helpers.each(b,d)):e(this);if(d.data&&d.ids){var g=c.createFrame(d.data);g.contextPath=c.appendContextPath(d.data.contextPath,d.name),d={data:g}}return f(b,d)})}}),define("handlebars/helpers/each",["exports","module","../utils","../exception"],function(a,b,c,d){"use strict";function e(a){return a&&a.__esModule?a:{"default":a}}var f=e(d);b.exports=function(a){a.registerHelper("each",function(a,b){function d(b,d,f){j&&(j.key=b,j.index=d,j.first=0===d,j.last=!!f,k&&(j.contextPath=k+b)),i+=e(a[b],{data:j,blockParams:c.blockParams([a[b],b],[k+b,null])})}if(!b)throw new f["default"]("Must pass iterator to #each");var e=b.fn,g=b.inverse,h=0,i="",j=void 0,k=void 0;if(b.data&&b.ids&&(k=c.appendContextPath(b.data.contextPath,b.ids[0])+"."),c.isFunction(a)&&(a=a.call(this)),b.data&&(j=c.createFrame(b.data)),a&&"object"==typeof a)if(c.isArray(a))for(var l=a.length;h<l;h++)h in a&&d(h,h,h===a.length-1);else if("function"==typeof Symbol&&a[Symbol.iterator]){for(var m=[],n=a[Symbol.iterator](),o=n.next();!o.done;o=n.next())m.push(o.value);a=m;for(var l=a.length;h<l;h++)d(h,h,h===a.length-1)}else!function(){var b=void 0;Object.keys(a).forEach(function(a){void 0!==b&&d(b,h-1),b=a,h++}),void 0!==b&&d(b,h-1,!0)}();return 0===h&&(i=g(this)),i})}}),define("handlebars/helpers/helper-missing",["exports","module","../exception"],function(a,b,c){"use strict";function d(a){return a&&a.__esModule?a:{"default":a}}var e=d(c);b.exports=function(a){a.registerHelper("helperMissing",function(){if(1!==arguments.length)throw new e["default"]('Missing helper: "'+arguments[arguments.length-1].name+'"')})}}),define("handlebars/helpers/if",["exports","module","../utils","../exception"],function(a,b,c,d){"use strict";function e(a){return a&&a.__esModule?a:{"default":a}}var f=e(d);b.exports=function(a){a.registerHelper("if",function(a,b){if(2!=arguments.length)throw new f["default"]("#if requires exactly one argument");return c.isFunction(a)&&(a=a.call(this)),!b.hash.includeZero&&!a||c.isEmpty(a)?b.inverse(this):b.fn(this)}),a.registerHelper("unless",function(b,c){if(2!=arguments.length)throw new f["default"]("#unless requires exactly one argument");return a.helpers["if"].call(this,b,{fn:c.inverse,inverse:c.fn,hash:c.hash})})}}),define("handlebars/helpers/log",["exports","module"],function(a,b){"use strict";b.exports=function(a){a.registerHelper("log",function(){for(var b=[void 0],c=arguments[arguments.length-1],d=0;d<arguments.length-1;d++)b.push(arguments[d]);var e=1;null!=c.hash.level?e=c.hash.level:c.data&&null!=c.data.level&&(e=c.data.level),b[0]=e,a.log.apply(a,b)})}}),define("handlebars/helpers/lookup",["exports","module"],function(a,b){"use strict";b.exports=function(a){a.registerHelper("lookup",function(a,b,c){return a?c.lookupProperty(a,b):a})}}),define("handlebars/helpers/with",["exports","module","../utils","../exception"],function(a,b,c,d){"use strict";function e(a){return a&&a.__esModule?a:{"default":a}}var f=e(d);b.exports=function(a){a.registerHelper("with",function(a,b){if(2!=arguments.length)throw new f["default"]("#with requires exactly one argument");c.isFunction(a)&&(a=a.call(this));var d=b.fn;if(c.isEmpty(a))return b.inverse(this);var e=b.data;return b.data&&b.ids&&(e=c.createFrame(b.data),e.contextPath=c.appendContextPath(b.data.contextPath,b.ids[0])),d(a,{data:e,blockParams:c.blockParams([a],[e&&e.contextPath])})})}}),define("handlebars/helpers",["exports","./helpers/block-helper-missing","./helpers/each","./helpers/helper-missing","./helpers/if","./helpers/log","./helpers/lookup","./helpers/with"],function(a,b,c,d,e,f,g,h){"use strict";function i(a){return a&&a.__esModule?a:{"default":a}}function j(a){l["default"](a),m["default"](a),n["default"](a),o["default"](a),p["default"](a),q["default"](a),r["default"](a)}function k(a,b,c){a.helpers[b]&&(a.hooks[b]=a.helpers[b],c||delete a.helpers[b])}a.__esModule=!0,a.registerDefaultHelpers=j,a.moveHelperToHooks=k;var l=i(b),m=i(c),n=i(d),o=i(e),p=i(f),q=i(g),r=i(h)}),define("handlebars/decorators/inline",["exports","module","../utils"],function(a,b,c){"use strict";b.exports=function(a){a.registerDecorator("inline",function(a,b,d,e){var f=a;return b.partials||(b.partials={},f=function(e,f){var g=d.partials;d.partials=c.extend({},g,b.partials);var h=a(e,f);return d.partials=g,h}),b.partials[e.args[0]]=e.fn,f})}}),define("handlebars/decorators",["exports","./decorators/inline"],function(a,b){"use strict";function c(a){return a&&a.__esModule?a:{"default":a}}function d(a){e["default"](a)}a.__esModule=!0,a.registerDefaultDecorators=d;var e=c(b)}),define("handlebars/logger",["exports","module","./utils"],function(a,b,c){"use strict";var d={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(a){if("string"==typeof a){var b=c.indexOf(d.methodMap,a.toLowerCase());a=b>=0?b:parseInt(a,10)}return a},log:function(a){if(a=d.lookupLevel(a),"undefined"!=typeof console&&d.lookupLevel(d.level)<=a){var b=d.methodMap[a];console[b]||(b="log");for(var c=arguments.length,e=Array(c>1?c-1:0),f=1;f<c;f++)e[f-1]=arguments[f];console[b].apply(console,e)}}};b.exports=d}),define("handlebars/internal/create-new-lookup-object",["exports","../utils"],function(a,b){"use strict";function c(){for(var a=arguments.length,c=Array(a),d=0;d<a;d++)c[d]=arguments[d];return b.extend.apply(void 0,[Object.create(null)].concat(c))}a.__esModule=!0,a.createNewLookupObject=c}),define("handlebars/internal/proto-access",["exports","./create-new-lookup-object","../logger"],function(a,b,c){"use strict";function d(a){return a&&a.__esModule?a:{"default":a}}function e(a){var c=Object.create(null);c.constructor=!1,c.__defineGetter__=!1,c.__defineSetter__=!1,c.__lookupGetter__=!1;var d=Object.create(null);return d.__proto__=!1,{properties:{whitelist:b.createNewLookupObject(d,a.allowedProtoProperties),defaultValue:a.allowProtoPropertiesByDefault},methods:{whitelist:b.createNewLookupObject(c,a.allowedProtoMethods),defaultValue:a.allowProtoMethodsByDefault}}}function f(a,b,c){return"function"==typeof a?g(b.methods,c):g(b.properties,c)}function g(a,b){return void 0!==a.whitelist[b]?a.whitelist[b]===!0:void 0!==a.defaultValue?a.defaultValue:(h(b),!1)}function h(a){k[a]!==!0&&(k[a]=!0,j["default"].log("error",'Handlebars: Access has been denied to resolve the property "'+a+'" because it is not an "own property" of its parent.\nYou can add a runtime option to disable the check or this warning:\nSee https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details'))}function i(){Object.keys(k).forEach(function(a){delete k[a]})}a.__esModule=!0,a.createProtoAccessControl=e,a.resultIsAllowed=f,a.resetLoggedProperties=i;var j=d(c),k=Object.create(null)}),define("handlebars/base",["exports","./utils","./exception","./helpers","./decorators","./logger","./internal/proto-access"],function(a,b,c,d,e,f,g){"use strict";function h(a){return a&&a.__esModule?a:{"default":a}}function i(a,b,c){this.helpers=a||{},this.partials=b||{},this.decorators=c||{},d.registerDefaultHelpers(this),e.registerDefaultDecorators(this)}a.__esModule=!0,a.HandlebarsEnvironment=i;var j=h(c),k=h(f),l="4.7.8";a.VERSION=l;var m=8;a.COMPILER_REVISION=m;var n=7;a.LAST_COMPATIBLE_COMPILER_REVISION=n;var o={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};a.REVISION_CHANGES=o;var p="[object Object]";i.prototype={constructor:i,logger:k["default"],log:k["default"].log,registerHelper:function(a,c){if(b.toString.call(a)===p){if(c)throw new j["default"]("Arg not supported with multiple helpers");b.extend(this.helpers,a)}else this.helpers[a]=c},unregisterHelper:function(a){delete this.helpers[a]},registerPartial:function(a,c){if(b.toString.call(a)===p)b.extend(this.partials,a);else{if("undefined"==typeof c)throw new j["default"]('Attempting to register a partial called "'+a+'" as undefined');this.partials[a]=c}},unregisterPartial:function(a){delete this.partials[a]},registerDecorator:function(a,c){if(b.toString.call(a)===p){if(c)throw new j["default"]("Arg not supported with multiple decorators");b.extend(this.decorators,a)}else this.decorators[a]=c},unregisterDecorator:function(a){delete this.decorators[a]},resetLoggedPropertyAccesses:function(){g.resetLoggedProperties()}};var q=k["default"].log;a.log=q,a.createFrame=b.createFrame,a.logger=k["default"]}),define("handlebars/safe-string",["exports","module"],function(a,b){"use strict";function c(a){this.string=a}c.prototype.toString=c.prototype.toHTML=function(){return""+this.string},b.exports=c}),define("handlebars/internal/wrapHelper",["exports"],function(a){"use strict";function b(a,b){if("function"!=typeof a)return a;var c=function(){var c=arguments[arguments.length-1];return arguments[arguments.length-1]=b(c),a.apply(this,arguments)};return c}a.__esModule=!0,a.wrapHelper=b}),define("handlebars/runtime",["exports","./utils","./exception","./base","./helpers","./internal/wrapHelper","./internal/proto-access"],function(a,b,c,d,e,f,g){"use strict";function h(a){return a&&a.__esModule?a:{"default":a}}function i(a){var b=a&&a[0]||1,c=d.COMPILER_REVISION;if(!(b>=d.LAST_COMPATIBLE_COMPILER_REVISION&&b<=d.COMPILER_REVISION)){if(b<d.LAST_COMPATIBLE_COMPILER_REVISION){var e=d.REVISION_CHANGES[c],f=d.REVISION_CHANGES[b];throw new s["default"]("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+e+") or downgrade your runtime to an older version ("+f+").")}throw new s["default"]("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+a[1]+").")}}function j(a,c){function d(d,e,f){f.hash&&(e=b.extend({},e,f.hash),f.ids&&(f.ids[0]=!0)),d=c.VM.resolvePartial.call(this,d,e,f);var g=b.extend({},f,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),h=c.VM.invokePartial.call(this,d,e,g);if(null==h&&c.compile&&(f.partials[f.name]=c.compile(d,a.compilerOptions,c),h=f.partials[f.name](e,g)),null!=h){if(f.indent){for(var i=h.split("\n"),j=0,k=i.length;j<k&&(i[j]||j+1!==k);j++)i[j]=f.indent+i[j];h=i.join("\n")}return h}throw new s["default"]("The partial "+f.name+" could not be compiled when running in runtime-only mode")}function f(b){function c(b){return""+a.main(i,b,i.helpers,i.partials,e,h,g)}var d=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],e=d.data;f._setup(d),!d.partial&&a.useData&&(e=o(b,e));var g=void 0,h=a.useBlockParams?[]:void 0;return a.useDepths&&(g=d.depths?b!=d.depths[0]?[b].concat(d.depths):d.depths:[b]),(c=p(a.main,c,i,d.depths||[],e,h))(b,d)}if(!c)throw new s["default"]("No environment passed to template");if(!a||!a.main)throw new s["default"]("Unknown template object: "+typeof a);a.main.decorator=a.main_d,c.VM.checkRevision(a.compiler);var h=a.compiler&&7===a.compiler[0],i={strict:function(a,b,c){if(!(a&&b in a))throw new s["default"]('"'+b+'" not defined in '+a,{loc:c});return i.lookupProperty(a,b)},lookupProperty:function(a,b){var c=a[b];return null==c?c:Object.prototype.hasOwnProperty.call(a,b)?c:g.resultIsAllowed(c,i.protoAccessControl,b)?c:void 0},lookup:function(a,b){for(var c=a.length,d=0;d<c;d++){var e=a[d]&&i.lookupProperty(a[d],b);if(null!=e)return a[d][b]}},lambda:function(a,b){return"function"==typeof a?a.call(b):a},escapeExpression:b.escapeExpression,invokePartial:d,fn:function(b){var c=a[b];return c.decorator=a[b+"_d"],c},programs:[],program:function(a,b,c,d,e){var f=this.programs[a],g=this.fn(a);return b||e||d||c?f=k(this,a,g,b,c,d,e):f||(f=this.programs[a]=k(this,a,g)),f},data:function(a,b){for(;a&&b--;)a=a._parent;return a},mergeIfNeeded:function(a,c){var d=a||c;return a&&c&&a!==c&&(d=b.extend({},c,a)),d},nullContext:Object.seal({}),noop:c.VM.noop,compilerInfo:a.compiler};return f.isTop=!0,f._setup=function(d){if(d.partial)i.protoAccessControl=d.protoAccessControl,i.helpers=d.helpers,i.partials=d.partials,i.decorators=d.decorators,i.hooks=d.hooks;else{var f=b.extend({},c.helpers,d.helpers);q(f,i),i.helpers=f,a.usePartial&&(i.partials=i.mergeIfNeeded(d.partials,c.partials)),(a.usePartial||a.useDecorators)&&(i.decorators=b.extend({},c.decorators,d.decorators)),i.hooks={},i.protoAccessControl=g.createProtoAccessControl(d);var j=d.allowCallsToHelperMissing||h;e.moveHelperToHooks(i,"helperMissing",j),e.moveHelperToHooks(i,"blockHelperMissing",j)}},f._child=function(b,c,d,e){if(a.useBlockParams&&!d)throw new s["default"]("must pass block params");if(a.useDepths&&!e)throw new s["default"]("must pass parent depths");return k(i,b,a[b],c,0,d,e)},f}function k(a,b,c,d,e,f,g){function h(b){var e=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],h=g;return!g||b==g[0]||b===a.nullContext&&null===g[0]||(h=[b].concat(g)),c(a,b,a.helpers,a.partials,e.data||d,f&&[e.blockParams].concat(f),h)}return h=p(c,h,a,g,d,f),h.program=b,h.depth=g?g.length:0,h.blockParams=e||0,h}function l(a,b,c){return a?a.call||c.name||(c.name=a,a=c.partials[a]):a="@partial-block"===c.name?c.data["partial-block"]:c.partials[c.name],a}function m(a,c,e){var f=e.data&&e.data["partial-block"];e.partial=!0,e.ids&&(e.data.contextPath=e.ids[0]||e.data.contextPath);var g=void 0;if(e.fn&&e.fn!==n&&!function(){e.data=d.createFrame(e.data);var a=e.fn;g=e.data["partial-block"]=function(b){var c=arguments.length<=1||void 0===arguments[1]?{}:arguments[1];return c.data=d.createFrame(c.data),c.data["partial-block"]=f,a(b,c)},a.partials&&(e.partials=b.extend({},e.partials,a.partials))}(),void 0===a&&g&&(a=g),void 0===a)throw new s["default"]("The partial "+e.name+" could not be found");if(a instanceof Function)return a(c,e)}function n(){return""}function o(a,b){return b&&"root"in b||(b=b?d.createFrame(b):{},b.root=a),b}function p(a,c,d,e,f,g){if(a.decorator){var h={};c=a.decorator(c,h,d,e&&e[0],f,g,e),b.extend(c,h)}return c}function q(a,b){Object.keys(a).forEach(function(c){var d=a[c];a[c]=r(d,b)})}function r(a,c){var d=c.lookupProperty;return f.wrapHelper(a,function(a){return b.extend({lookupProperty:d},a)})}a.__esModule=!0,a.checkRevision=i,a.template=j,a.wrapProgram=k,a.resolvePartial=l,a.invokePartial=m,a.noop=n;var s=h(c)}),define("handlebars/no-conflict",["exports","module"],function(a,b){"use strict";b.exports=function(a){!function(){"object"!=typeof globalThis&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__)}();var b=globalThis.Handlebars;a.noConflict=function(){return globalThis.Handlebars===a&&(globalThis.Handlebars=b),a}}}),define("handlebars.runtime",["exports","module","./handlebars/base","./handlebars/safe-string","./handlebars/exception","./handlebars/utils","./handlebars/runtime","./handlebars/no-conflict"],function(a,b,c,d,e,f,g,h){"use strict";function i(a){return a&&a.__esModule?a:{"default":a}}function j(){var a=new c.HandlebarsEnvironment;return f.extend(a,c),a.SafeString=k["default"],a.Exception=l["default"],a.Utils=f,a.escapeExpression=f.escapeExpression,a.VM=g,a.template=function(b){return g.template(b,a)},a}var k=i(d),l=i(e),m=i(h),n=j();n.create=j,m["default"](n),n["default"]=n,b.exports=n});