#!/usr/bin/env node

/**
 * Environment Setup Script
 * Creates and configures .env files for both frontend and backend
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => {
  return new Promise(resolve => rl.question(query, resolve));
};

// Generate a random JWT secret
const generateJWTSecret = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let result = '';
  for (let i = 0; i < 64; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Setup backend environment
const setupBackendEnv = async () => {
  log('🔧 Setting up backend environment...', 'cyan');
  
  const backendEnvPath = path.join(projectRoot, 'backend', '.env');
  const backendEnvExamplePath = path.join(projectRoot, 'backend', '.env.example');
  
  let envContent = '';
  
  // Read example file if it exists
  if (fs.existsSync(backendEnvExamplePath)) {
    envContent = fs.readFileSync(backendEnvExamplePath, 'utf8');
  }
  
  // Get user input for configuration
  const mongoUri = await question('MongoDB URI (default: mongodb://localhost:27017/projectbuzz): ') || 'mongodb://localhost:27017/projectbuzz';
  const port = await question('Backend port (default: 5000): ') || '5000';
  const frontendUrl = await question('Frontend URL (default: http://localhost:5173): ') || 'http://localhost:5173';
  const jwtSecret = await question(`JWT Secret (press Enter to generate random): `) || generateJWTSecret();
  
  // Update environment variables
  const envVars = {
    MONGO_URI: mongoUri,
    PORT: port,
    NODE_ENV: 'development',
    FRONTEND_URL: frontendUrl,
    BACKEND_URL: `http://localhost:${port}`,
    JWT_SECRET: jwtSecret,
    JWT_EXPIRES_IN: '7d',
    PORT_FALLBACK_START: '5001',
    PORT_FALLBACK_END: '5010'
  };
  
  // Build environment file content
  let newEnvContent = '# ProjectBuzz Backend Environment Configuration\n';
  newEnvContent += '# Generated by setup script\n\n';
  
  for (const [key, value] of Object.entries(envVars)) {
    newEnvContent += `${key}=${value}\n`;
  }
  
  // Add optional configurations from example
  if (envContent.includes('RAZORPAY_')) {
    newEnvContent += '\n# Payment Configuration (Razorpay)\n';
    newEnvContent += 'RAZORPAY_KEY_ID=your-razorpay-key-id\n';
    newEnvContent += 'RAZORPAY_KEY_SECRET=your-razorpay-key-secret\n';
    newEnvContent += 'RAZORPAY_WEBHOOK_SECRET=your-razorpay-webhook-secret\n';
  }
  
  fs.writeFileSync(backendEnvPath, newEnvContent);
  log(`✅ Backend .env file created at: ${backendEnvPath}`, 'green');
};

// Setup frontend environment
const setupFrontendEnv = async () => {
  log('🔧 Setting up frontend environment...', 'cyan');
  
  const frontendEnvPath = path.join(projectRoot, 'frontend', '.env');
  const backendEnvPath = path.join(projectRoot, 'backend', '.env');
  
  // Read backend port from backend .env
  let backendPort = '5000';
  if (fs.existsSync(backendEnvPath)) {
    const backendEnv = fs.readFileSync(backendEnvPath, 'utf8');
    const portMatch = backendEnv.match(/PORT=(\d+)/);
    if (portMatch) {
      backendPort = portMatch[1];
    }
  }
  
  const frontendPort = await question('Frontend port (default: 5173): ') || '5173';
  
  const envVars = {
    VITE_API_URL: `http://localhost:${backendPort}/api`,
    VITE_BACKEND_URL: `http://localhost:${backendPort}`,
    VITE_DEV_PORT: frontendPort,
    VITE_DEV_HOST: 'localhost',
    VITE_APP_NAME: 'ProjectBuzz',
    VITE_APP_VERSION: '1.0.0',
    VITE_ENABLE_DEBUG: 'true',
    VITE_RAZORPAY_ENVIRONMENT: 'test'
  };
  
  let envContent = '# ProjectBuzz Frontend Environment Configuration\n';
  envContent += '# Generated by setup script\n\n';
  
  for (const [key, value] of Object.entries(envVars)) {
    envContent += `${key}=${value}\n`;
  }
  
  fs.writeFileSync(frontendEnvPath, envContent);
  log(`✅ Frontend .env file created at: ${frontendEnvPath}`, 'green');
};

// Main function
const main = async () => {
  try {
    log('🚀 ProjectBuzz Environment Setup', 'cyan');
    log('=================================', 'cyan');
    log('');
    log('This script will help you set up environment files for development.', 'yellow');
    log('');
    
    // Check if .env files already exist
    const backendEnvExists = fs.existsSync(path.join(projectRoot, 'backend', '.env'));
    const frontendEnvExists = fs.existsSync(path.join(projectRoot, 'frontend', '.env'));
    
    if (backendEnvExists || frontendEnvExists) {
      log('⚠️  Existing .env files detected:', 'yellow');
      if (backendEnvExists) log('   - backend/.env', 'yellow');
      if (frontendEnvExists) log('   - frontend/.env', 'yellow');
      log('');
      
      const overwrite = await question('Do you want to overwrite existing files? (y/N): ');
      if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
        log('Setup cancelled.', 'yellow');
        rl.close();
        return;
      }
    }
    
    // Setup environments
    await setupBackendEnv();
    log('');
    await setupFrontendEnv();
    
    log('');
    log('🎉 Environment setup complete!', 'green');
    log('');
    log('Next steps:', 'cyan');
    log('1. Review and update the generated .env files if needed', 'blue');
    log('2. Make sure MongoDB is running', 'blue');
    log('3. Run "npm run dev" to start the development environment', 'blue');
    log('');
    log('Useful commands:', 'cyan');
    log('- npm run check-ports  : Check port availability', 'blue');
    log('- npm run kill-ports   : Clean up busy ports', 'blue');
    log('- npm run dev          : Start development environment', 'blue');
    
  } catch (error) {
    log(`❌ Error: ${error.message}`, 'red');
  } finally {
    rl.close();
  }
};

main();
