{"name": "projectbuzz", "version": "1.0.0", "description": "Digital Project Marketplace - Buy and sell amazing digital projects", "type": "module", "scripts": {"setup": "node scripts/setup-env.js", "dev": "node scripts/dev-start.js", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules", "reset": "npm run clean && npm run install:all", "check-ports": "node scripts/check-ports.js", "kill-ports": "node scripts/kill-ports.js", "atlas:migrate": "node scripts/atlas-migration.js", "atlas:setup": "node scripts/atlas-setup.js", "atlas:health": "node scripts/atlas-health-check.js", "production:validate": "node scripts/validate-production.js", "production:setup": "npm run atlas:migrate && npm run atlas:setup", "production:verify": "npm run production:validate && npm run atlas:health", "deployment:summary": "node scripts/deployment-summary.js", "test:connection": "node scripts/test-connection.js", "atlas:cleanup": "node scripts/atlas-cleanup.js", "atlas:test-auth": "node scripts/test-atlas-auth.js", "mark-featured": "node scripts/mark-featured-projects.js", "postinstall": "echo '🎉 Installation complete! Run npm run setup to configure environment files.'"}, "keywords": ["marketplace", "digital-projects", "react", "node.js", "mongodb", "express"], "author": "ProjectBuzz Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/projectbuzz.git"}, "bugs": {"url": "https://github.com/your-username/projectbuzz/issues"}, "homepage": "https://github.com/your-username/projectbuzz#readme", "dependencies": {"axios": "^1.9.0", "mongodb": "^6.17.0"}}