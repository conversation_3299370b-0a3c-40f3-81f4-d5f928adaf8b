<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Razorpay Frontend Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        button {
            background: #000;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #333;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Razorpay Frontend Integration</h1>
    
    <div>
        <h3>Test Payment Details:</h3>
        <p><strong>Amount:</strong> ₹100</p>
        <p><strong>Order ID:</strong> order_test_123</p>
        <p><strong>Key ID:</strong> rzp_test_71doc1660gWGcy</p>
    </div>

    <button onclick="testRazorpayPayment()">🚀 Test Razorpay Payment</button>
    
    <div id="log" class="log">Ready to test...\n</div>

    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        function testRazorpayPayment() {
            log('🧪 Starting Razorpay payment test...');
            
            // Check if Razorpay SDK is loaded
            if (!window.Razorpay) {
                log('❌ Razorpay SDK not loaded');
                return;
            }
            
            log('✅ Razorpay SDK loaded successfully');

            // Test payment options
            const options = {
                key: 'rzp_test_71doc1660gWGcy', // Your test key
                amount: 10000, // Amount in paise (₹100)
                currency: 'INR',
                name: 'ProjectBuzz Test',
                description: 'Test Payment',
                order_id: 'order_test_123', // This would normally come from backend
                prefill: {
                    name: 'Test User',
                    email: '<EMAIL>',
                    contact: '9999999999'
                },
                theme: {
                    color: '#000000'
                },
                handler: function(response) {
                    log('✅ Payment successful!');
                    log('Payment ID: ' + response.razorpay_payment_id);
                    log('Order ID: ' + response.razorpay_order_id);
                    log('Signature: ' + response.razorpay_signature);
                },
                modal: {
                    ondismiss: function() {
                        log('❌ Payment cancelled by user');
                    }
                }
            };

            log('📤 Opening Razorpay checkout with options:');
            log(JSON.stringify(options, null, 2));

            try {
                const rzp = new Razorpay(options);
                rzp.open();
                log('✅ Razorpay checkout opened successfully');
            } catch (error) {
                log('❌ Error opening Razorpay checkout: ' + error.message);
                console.error('Razorpay error:', error);
            }
        }

        // Test SDK loading on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (window.Razorpay) {
                    log('✅ Razorpay SDK loaded on page load');
                } else {
                    log('❌ Razorpay SDK not loaded on page load');
                }
            }, 1000);
        });
    </script>
</body>
</html>
