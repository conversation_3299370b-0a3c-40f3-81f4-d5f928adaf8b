# MongoDB Configuration
# Local Development
MONGO_URI=mongodb://localhost:27017/projectbuzz

# Production MongoDB Atlas (replace with your Atlas connection string)
# MONGO_URI=mongodb+srv://<username>:<password>@<cluster-name>.mongodb.net/projectbuzz?retryWrites=true&w=majority

# MongoDB Atlas Configuration
MONGODB_ATLAS_URI=mongodb+srv://<username>:<password>@<cluster-name>.mongodb.net/projectbuzz?retryWrites=true&w=majority
MONGODB_ATLAS_DB_NAME=projectbuzz

# Database Connection Settings
DB_MAX_POOL_SIZE=10
DB_SERVER_SELECTION_TIMEOUT=5000
DB_SOCKET_TIMEOUT=45000
DB_CONNECT_TIMEOUT=10000
DB_MAX_IDLE_TIME=30000

# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:5000

# Production URLs (update for your production deployment)
PRODUCTION_FRONTEND_URL=https://your-domain.com
PRODUCTION_BACKEND_URL=https://api.your-domain.com

# Auto Port Detection (fallback ports if main port is busy)
PORT_FALLBACK_START=5001
PORT_FALLBACK_END=5010

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-minimum-32-characters-long
JWT_EXPIRES_IN=7d

# Razorpay Payment Gateway Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret

# Security Configuration
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:5173,https://your-domain.com

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=Doma@ji12

# Email Settings
FROM_NAME=ProjectBuzz
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
APP_NAME=ProjectBuzz

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
MAX_FILES_PER_PROJECT=5

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
