<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProjectBuzz CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .image-test {
            border: 2px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            border-color: #4CAF50;
            background-color: #f0f8f0;
        }
        .error {
            border-color: #f44336;
            background-color: #fdf0f0;
        }
        img {
            max-width: 200px;
            height: auto;
            display: block;
            margin: 10px 0;
        }
        .status {
            font-weight: bold;
            margin: 5px 0;
        }
        .success .status {
            color: #4CAF50;
        }
        .error .status {
            color: #f44336;
        }
    </style>
</head>
<body>
    <h1>ProjectBuzz CORS Test</h1>
    <p>This page tests if images from the ProjectBuzz backend (localhost:5002) can be loaded from a different origin.</p>
    
    <div class="test-section">
        <h2>Image Loading Tests</h2>
        
        <div class="image-test" id="test1">
            <h3>Test 1: Direct Image Tag</h3>
            <div class="status">Loading...</div>
            <img id="img1" src="http://localhost:5002/api/projects/images/1749135632993_68417d76abea618379abe37f_7r3gqg.png" alt="Test Image 1">
        </div>
        
        <div class="image-test" id="test2">
            <h3>Test 2: JavaScript Image Loading</h3>
            <div class="status">Loading...</div>
            <img id="img2" alt="Test Image 2">
        </div>
        
        <div class="image-test" id="test3">
            <h3>Test 3: Fetch API Test</h3>
            <div class="status">Loading...</div>
            <div id="fetch-result"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>CORS Headers Test</h2>
        <div id="cors-headers"></div>
    </div>

    <script>
        // Test 1: Direct image tag (handled by onload/onerror)
        const img1 = document.getElementById('img1');
        const test1 = document.getElementById('test1');
        
        img1.onload = function() {
            test1.classList.add('success');
            test1.querySelector('.status').textContent = '✅ SUCCESS: Image loaded successfully';
        };
        
        img1.onerror = function() {
            test1.classList.add('error');
            test1.querySelector('.status').textContent = '❌ ERROR: Failed to load image (CORS blocked)';
        };
        
        // Test 2: JavaScript image loading
        const img2 = document.getElementById('img2');
        const test2 = document.getElementById('test2');
        
        const testImg = new Image();
        testImg.crossOrigin = 'anonymous';
        testImg.onload = function() {
            test2.classList.add('success');
            test2.querySelector('.status').textContent = '✅ SUCCESS: JavaScript image loading works';
            img2.src = testImg.src;
        };
        
        testImg.onerror = function() {
            test2.classList.add('error');
            test2.querySelector('.status').textContent = '❌ ERROR: JavaScript image loading failed (CORS blocked)';
        };
        
        testImg.src = 'http://localhost:5002/api/projects/images/1749111063556_68414f7bcaca9badd6594b01_l7331wh.png';
        
        // Test 3: Fetch API test
        const test3 = document.getElementById('test3');
        const fetchResult = document.getElementById('fetch-result');
        
        fetch('http://localhost:5002/api/projects/images/1749081443388_6840dcc235749ec622b56a25_cos6h.png', {
            method: 'HEAD',
            mode: 'cors'
        })
        .then(response => {
            if (response.ok) {
                test3.classList.add('success');
                test3.querySelector('.status').textContent = '✅ SUCCESS: Fetch API works';
                fetchResult.innerHTML = `
                    <strong>Response Headers:</strong><br>
                    Status: ${response.status}<br>
                    CORS Headers: ${response.headers.get('Access-Control-Allow-Origin') || 'Not set'}
                `;
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        })
        .catch(error => {
            test3.classList.add('error');
            test3.querySelector('.status').textContent = '❌ ERROR: Fetch API failed';
            fetchResult.innerHTML = `<strong>Error:</strong> ${error.message}`;
        });
        
        // CORS Headers test
        fetch('http://localhost:5002/api/projects/images/1749081443388_6840dcc235749ec622b56a25_cos6h.png', {
            method: 'HEAD',
            mode: 'cors'
        })
        .then(response => {
            const corsHeaders = document.getElementById('cors-headers');
            const headers = [
                'Access-Control-Allow-Origin',
                'Access-Control-Allow-Methods',
                'Access-Control-Allow-Headers',
                'Cross-Origin-Resource-Policy',
                'Cross-Origin-Embedder-Policy'
            ];
            
            let headerInfo = '<h3>CORS Response Headers:</h3><ul>';
            headers.forEach(header => {
                const value = response.headers.get(header);
                headerInfo += `<li><strong>${header}:</strong> ${value || 'Not set'}</li>`;
            });
            headerInfo += '</ul>';
            
            corsHeaders.innerHTML = headerInfo;
        })
        .catch(error => {
            document.getElementById('cors-headers').innerHTML = `<p style="color: red;">Failed to fetch headers: ${error.message}</p>`;
        });
    </script>
</body>
</html>
