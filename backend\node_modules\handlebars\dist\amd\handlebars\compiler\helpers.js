define(['exports', '../exception'], function (exports, _exception) {
  'use strict';

  exports.__esModule = true;
  exports.SourceLocation = SourceLocation;
  exports.id = id;
  exports.stripFlags = stripFlags;
  exports.stripComment = stripComment;
  exports.preparePath = preparePath;
  exports.prepareMustache = prepareMustache;
  exports.prepareRawBlock = prepareRawBlock;
  exports.prepareBlock = prepareBlock;
  exports.prepareProgram = prepareProgram;
  exports.preparePartialBlock = preparePartialBlock;
  // istanbul ignore next

  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

  var _Exception = _interopRequireDefault(_exception);

  function validateClose(open, close) {
    close = close.path ? close.path.original : close;

    if (open.path.original !== close) {
      var errorNode = { loc: open.path.loc };

      throw new _Exception['default'](open.path.original + " doesn't match " + close, errorNode);
    }
  }

  function SourceLocation(source, locInfo) {
    this.source = source;
    this.start = {
      line: locInfo.first_line,
      column: locInfo.first_column
    };
    this.end = {
      line: locInfo.last_line,
      column: locInfo.last_column
    };
  }

  function id(token) {
    if (/^\[.*\]$/.test(token)) {
      return token.substring(1, token.length - 1);
    } else {
      return token;
    }
  }

  function stripFlags(open, close) {
    return {
      open: open.charAt(2) === '~',
      close: close.charAt(close.length - 3) === '~'
    };
  }

  function stripComment(comment) {
    return comment.replace(/^\{\{~?!-?-?/, '').replace(/-?-?~?\}\}$/, '');
  }

  function preparePath(data, parts, loc) {
    loc = this.locInfo(loc);

    var original = data ? '@' : '',
        dig = [],
        depth = 0;

    for (var i = 0, l = parts.length; i < l; i++) {
      var part = parts[i].part,

      // If we have [] syntax then we do not treat path references as operators,
      // i.e. foo.[this] resolves to approximately context.foo['this']
      isLiteral = parts[i].original !== part;
      original += (parts[i].separator || '') + part;

      if (!isLiteral && (part === '..' || part === '.' || part === 'this')) {
        if (dig.length > 0) {
          throw new _Exception['default']('Invalid path: ' + original, { loc: loc });
        } else if (part === '..') {
          depth++;
        }
      } else {
        dig.push(part);
      }
    }

    return {
      type: 'PathExpression',
      data: data,
      depth: depth,
      parts: dig,
      original: original,
      loc: loc
    };
  }

  function prepareMustache(path, params, hash, open, strip, locInfo) {
    // Must use charAt to support IE pre-10
    var escapeFlag = open.charAt(3) || open.charAt(2),
        escaped = escapeFlag !== '{' && escapeFlag !== '&';

    var decorator = /\*/.test(open);
    return {
      type: decorator ? 'Decorator' : 'MustacheStatement',
      path: path,
      params: params,
      hash: hash,
      escaped: escaped,
      strip: strip,
      loc: this.locInfo(locInfo)
    };
  }

  function prepareRawBlock(openRawBlock, contents, close, locInfo) {
    validateClose(openRawBlock, close);

    locInfo = this.locInfo(locInfo);
    var program = {
      type: 'Program',
      body: contents,
      strip: {},
      loc: locInfo
    };

    return {
      type: 'BlockStatement',
      path: openRawBlock.path,
      params: openRawBlock.params,
      hash: openRawBlock.hash,
      program: program,
      openStrip: {},
      inverseStrip: {},
      closeStrip: {},
      loc: locInfo
    };
  }

  function prepareBlock(openBlock, program, inverseAndProgram, close, inverted, locInfo) {
    if (close && close.path) {
      validateClose(openBlock, close);
    }

    var decorator = /\*/.test(openBlock.open);

    program.blockParams = openBlock.blockParams;

    var inverse = undefined,
        inverseStrip = undefined;

    if (inverseAndProgram) {
      if (decorator) {
        throw new _Exception['default']('Unexpected inverse block on decorator', inverseAndProgram);
      }

      if (inverseAndProgram.chain) {
        inverseAndProgram.program.body[0].closeStrip = close.strip;
      }

      inverseStrip = inverseAndProgram.strip;
      inverse = inverseAndProgram.program;
    }

    if (inverted) {
      inverted = inverse;
      inverse = program;
      program = inverted;
    }

    return {
      type: decorator ? 'DecoratorBlock' : 'BlockStatement',
      path: openBlock.path,
      params: openBlock.params,
      hash: openBlock.hash,
      program: program,
      inverse: inverse,
      openStrip: openBlock.strip,
      inverseStrip: inverseStrip,
      closeStrip: close && close.strip,
      loc: this.locInfo(locInfo)
    };
  }

  function prepareProgram(statements, loc) {
    if (!loc && statements.length) {
      var firstLoc = statements[0].loc,
          lastLoc = statements[statements.length - 1].loc;

      /* istanbul ignore else */
      if (firstLoc && lastLoc) {
        loc = {
          source: firstLoc.source,
          start: {
            line: firstLoc.start.line,
            column: firstLoc.start.column
          },
          end: {
            line: lastLoc.end.line,
            column: lastLoc.end.column
          }
        };
      }
    }

    return {
      type: 'Program',
      body: statements,
      strip: {},
      loc: loc
    };
  }

  function preparePartialBlock(open, program, close, locInfo) {
    validateClose(open, close);

    return {
      type: 'PartialBlockStatement',
      name: open.path,
      params: open.params,
      hash: open.hash,
      program: program,
      openStrip: open.strip,
      closeStrip: close && close.strip,
      loc: this.locInfo(locInfo)
    };
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
