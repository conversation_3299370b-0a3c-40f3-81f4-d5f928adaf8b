# Production Environment Configuration for ProjectBuzz
# Copy this file to .env.production and update with your production values

# Environment
NODE_ENV=production

# MongoDB Atlas Configuration (REQUIRED)
MONGO_URI=mongodb+srv://<username>:<password>@<cluster-name>.mongodb.net/projectbuzz?retryWrites=true&w=majority
MONGODB_ATLAS_DB_NAME=projectbuzz

# Database Connection Settings (Production Optimized)
DB_MAX_POOL_SIZE=20
DB_SERVER_SELECTION_TIMEOUT=10000
DB_SOCKET_TIMEOUT=60000
DB_CONNECT_TIMEOUT=15000
DB_MAX_IDLE_TIME=60000
DB_MIN_POOL_SIZE=5

# Server Configuration
PORT=5000
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://api.your-domain.com

# JWT Configuration (CRITICAL - Use strong secrets)
JWT_SECRET=your-production-jwt-secret-minimum-64-characters-long-and-very-secure
JWT_EXPIRES_IN=7d

# Razorpay Payment Gateway (Production)
RAZORPAY_KEY_ID=rzp_live_your_production_key_id
RAZORPAY_KEY_SECRET=your_production_razorpay_secret
RAZORPAY_WEBHOOK_SECRET=your_production_webhook_secret

# Security Configuration
BCRYPT_ROUNDS=14
CORS_ORIGIN=https://your-domain.com,https://www.your-domain.com

# Email Configuration (Production SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-production-email-app-password

# Email Settings
FROM_NAME=ProjectBuzz
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
APP_NAME=ProjectBuzz

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
MAX_FILES_PER_PROJECT=5

# Rate Limiting (Production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200

# Logging
LOG_LEVEL=warn
LOG_FILE=logs/production.log

# Health Check
HEALTH_CHECK_ENABLED=true

# Session Configuration
SESSION_SECRET=your-production-session-secret-64-characters-minimum

# SSL/TLS Configuration
SSL_ENABLED=true
FORCE_HTTPS=true

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
