{"name": "handlebars", "barename": "handlebars", "version": "4.7.8", "description": "Handlebars provides the power necessary to let you build semantic templates effectively with no frustration", "homepage": "https://www.handlebarsjs.com/", "keywords": ["handlebars", "mustache", "template", "html"], "repository": {"type": "git", "url": "https://github.com/handlebars-lang/handlebars.js.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "readmeFilename": "README.md", "engines": {"node": ">=0.4.7"}, "dependencies": {"minimist": "^1.2.5", "neo-async": "^2.6.2", "source-map": "^0.6.1", "wordwrap": "^1.0.0"}, "optionalDependencies": {"uglify-js": "^3.1.4"}, "devDependencies": {"@playwright/test": "^1.17.1", "aws-sdk": "^2.1.49", "babel-loader": "^5.0.0", "babel-runtime": "^5.1.10", "benchmark": "~1.0", "chai": "^4.2.0", "chai-diff": "^1.0.1", "concurrently": "^5.0.0", "dirty-chai": "^2.0.1", "dtslint": "^0.5.5", "dustjs-linkedin": "^2.0.2", "eco": "~1.1.0-rc-3", "eslint": "^6.7.2", "eslint-config-prettier": "^6.7.0", "eslint-plugin-compat": "^3.13.0", "eslint-plugin-es5": "^1.4.1", "fs-extra": "^8.1.0", "grunt": "^1.0.4", "grunt-babel": "^5.0.0", "grunt-cli": "^1", "grunt-contrib-clean": "^1", "grunt-contrib-concat": "^1", "grunt-contrib-connect": "^1", "grunt-contrib-copy": "^1", "grunt-contrib-requirejs": "^1", "grunt-contrib-uglify": "^1", "grunt-contrib-watch": "^1.1.0", "grunt-shell": "^4.0.0", "grunt-webpack": "^1.0.8", "husky": "^3.1.0", "jison": "~0.3.0", "lint-staged": "^9.5.0", "mocha": "^5", "mock-stdin": "^0.3.0", "mustache": "^2.1.3", "nyc": "^14.1.1", "prettier": "^1.19.1", "semver": "^5.0.1", "sinon": "^7.5.0", "typescript": "^3.4.3", "underscore": "^1.5.1", "webpack": "^1.12.6", "webpack-dev-server": "^1.12.1"}, "main": "lib/index.js", "types": "types/index.d.ts", "browser": "./dist/cjs/handlebars.js", "bin": {"handlebars": "bin/handlebars"}, "scripts": {"build": "grunt build", "release": "npm run build && grunt release", "format": "prettier --write '**/*.js' && eslint --fix .", "lint": "npm run lint:eslint && npm run lint:prettier && npm run lint:types", "lint:eslint": "eslint --max-warnings 0 .", "lint:prettier": "prettier --check '**/*.js'", "lint:types": "dtslint types", "test": "npm run test:mocha", "test:mocha": "grunt build && grunt test", "test:browser": "playwright test --config tests/browser/playwright.config.js tests/browser/spec.js", "test:integration": "grunt integration-tests", "test:serve": "grunt connect:server:keepalive", "extensive-tests-and-publish-to-aws": "npx mocha tasks/tests/ && grunt --stack extensive-tests-and-publish-to-aws", "--- combined tasks ---": "", "check-before-pull-request": "concurrently --kill-others-on-fail npm:lint npm:test"}, "jspm": {"main": "handlebars", "directories": {"lib": "dist/amd"}, "buildConfig": {"minify": true}}, "files": ["bin", "dist/*.js", "dist/amd/**/*.js", "dist/cjs/**/*.js", "lib", "release-notes.md", "runtime.js", "types/*.d.ts", "runtime.d.ts"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,css,json}": ["prettier --write", "git add"], "*.js": ["eslint --fix", "git add"]}}