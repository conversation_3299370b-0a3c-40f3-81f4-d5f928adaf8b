{"name": "backend", "version": "1.0.0", "description": "Backend server for the application", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-server.js", "test-email": "node test-email-simple.js"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.19.2", "express-validator": "^7.0.1", "handlebars": "^4.7.8", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.3.2", "motion": "^12.16.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "razorpay": "^2.9.6"}, "devDependencies": {"nodemon": "^3.1.0"}}