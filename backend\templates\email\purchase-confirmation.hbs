<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Purchase Confirmation - {{appName}}</title>
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      Oxygen, Ubuntu, Cantarell, sans-serif; line-height: 1.6; color: #333;
      max-width: 600px; margin: 0 auto; padding: 20px; background-color:
      #f8fafc; } .container { background: white; border-radius: 8px; padding:
      40px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); } .header { text-align:
      center; margin-bottom: 30px; } .logo { font-size: 24px; font-weight: bold;
      color: #1f2937; margin-bottom: 10px; } .title { color: #059669; font-size:
      28px; font-weight: bold; margin-bottom: 10px; } .subtitle { color:
      #6b7280; font-size: 16px; } .order-details { background: #f9fafb;
      border-radius: 6px; padding: 20px; margin: 30px 0; } .order-row { display:
      flex; justify-content: space-between; margin-bottom: 10px; padding: 5px 0;
      } .order-row:last-child { margin-bottom: 0; border-top: 1px solid #e5e7eb;
      padding-top: 15px; font-weight: bold; } .btn { display: inline-block;
      background: #000000; color: white; padding: 12px 24px; text-decoration:
      none; border-radius: 6px; font-weight: 500; margin: 20px 0; } .btn:hover {
      background: #374151; } .footer { text-align: center; margin-top: 40px;
      padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280;
      font-size: 14px; } .support { background: #fef3c7; border: 1px solid
      #f59e0b; border-radius: 6px; padding: 15px; margin: 20px 0; }
      .support-title { font-weight: bold; color: #92400e; margin-bottom: 5px; }
      .support-text { color: #92400e; font-size: 14px; }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="logo">{{appName}}</div>
        <h1 class="title">🎉 Purchase Confirmed!</h1>
        <p class="subtitle">Thank you for your purchase, {{userName}}!</p>
      </div>

      <p>Your purchase has been successfully processed. You can now access your
        digital product.</p>

      <div class="order-details">
        <h3 style="margin-top: 0; color: #1f2937;">Order Details</h3>
        <div class="order-row">
          <span>Order ID:</span>
          <span><strong>{{orderId}}</strong></span>
        </div>
        {{#if transactionId}}
          <div class="order-row">
            <span>Transaction ID:</span>
            <span>{{transactionId}}</span>
          </div>
        {{/if}}
        <div class="order-row">
          <span>Project:</span>
          <span><strong>{{projectTitle}}</strong></span>
        </div>
        <div class="order-row">
          <span>Purchase Date:</span>
          <span>{{orderDate}}</span>
        </div>
        <div class="order-row">
          <span>Total Amount:</span>
          <span><strong>₹{{projectPrice}}</strong></span>
        </div>
      </div>

      <div style="text-align: center;">
        <a href="{{downloadUrl}}" class="btn">Access Your Purchase</a>
      </div>

      <div class="support">
        <div class="support-title">📥 Download Instructions</div>
        <div class="support-text">
          Click the button above to access your dashboard where you can download
          your purchased project files. Your purchase is available for unlimited
          downloads.
        </div>
      </div>

      <p>If you have any questions or need assistance, please don't hesitate to
        contact our support team.</p>

      <div class="footer">
        <p>Thank you for choosing {{appName}}!</p>
        <p>
          <a href="{{appUrl}}" style="color: #6b7280;">Visit {{appName}}</a>
          |
          <a href="mailto:{{supportEmail}}" style="color: #6b7280;">Contact
            Support</a>
        </p>
        <p>&copy; {{currentYear}} {{appName}}. All rights reserved.</p>
      </div>
    </div>
  </body>
</html>